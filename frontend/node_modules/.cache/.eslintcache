[{"/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx": "1", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx": "2", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx": "3", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx": "4", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx": "7", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx": "8", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts": "9", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx": "10", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts": "11", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx": "12", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataUpdate.tsx": "13"}, {"size": 272, "mtime": *************, "results": "14", "hashOfConfig": "15"}, {"size": 1393, "mtime": *************, "results": "16", "hashOfConfig": "15"}, {"size": 2691, "mtime": *************, "results": "17", "hashOfConfig": "15"}, {"size": 646, "mtime": *************, "results": "18", "hashOfConfig": "15"}, {"size": 1976, "mtime": *************, "results": "19", "hashOfConfig": "15"}, {"size": 5101, "mtime": *************, "results": "20", "hashOfConfig": "15"}, {"size": 29678, "mtime": 1753039684400, "results": "21", "hashOfConfig": "15"}, {"size": 2050, "mtime": 1752747944753, "results": "22", "hashOfConfig": "15"}, {"size": 1053, "mtime": 1753091870631, "results": "23", "hashOfConfig": "15"}, {"size": 10389, "mtime": 1753033841869, "results": "24", "hashOfConfig": "15"}, {"size": 2912, "mtime": 1753033799016, "results": "25", "hashOfConfig": "15"}, {"size": 17705, "mtime": 1753089891679, "results": "26", "hashOfConfig": "15"}, {"size": 11391, "mtime": 1753088395990, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ix7n9v", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx", ["67"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx", ["68"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx", ["69", "70", "71", "72", "73"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataUpdate.tsx", ["74", "75"], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 4, "column": 24, "nodeType": "78", "messageId": "79", "endLine": 4, "endColumn": 41}, {"ruleId": "76", "severity": 1, "message": "80", "line": 337, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 337, "endColumn": 26}, {"ruleId": "76", "severity": 1, "message": "81", "line": 3, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 3, "endColumn": 24}, {"ruleId": "76", "severity": 1, "message": "82", "line": 3, "column": 42, "nodeType": "78", "messageId": "79", "endLine": 3, "endColumn": 58}, {"ruleId": "76", "severity": 1, "message": "83", "line": 56, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 56, "endColumn": 26}, {"ruleId": "76", "severity": 1, "message": "84", "line": 65, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 65, "endColumn": 23}, {"ruleId": "85", "severity": 1, "message": "86", "line": 144, "column": 6, "nodeType": "87", "endLine": 144, "endColumn": 94, "suggestions": "88"}, {"ruleId": "76", "severity": 1, "message": "89", "line": 4, "column": 43, "nodeType": "78", "messageId": "79", "endLine": 4, "endColumn": 50}, {"ruleId": "85", "severity": 1, "message": "90", "line": 221, "column": 6, "nodeType": "87", "endLine": 221, "endColumn": 8, "suggestions": "91"}, "@typescript-eslint/no-unused-vars", "'DashboardOutlined' is defined but never used.", "Identifier", "unusedVar", "'handleForceLogout' is assigned a value but never used.", "'SearchOutlined' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'currentDataRange' is assigned a value but never used.", "'platformConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDataList'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["92"], "'Divider' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkRunningTask' and 'pollingInterval'. Either include them or remove the dependency array.", ["93"], {"desc": "94", "fix": "95"}, {"desc": "96", "fix": "97"}, "Update the dependencies array to be: [selectedAccount, selectedDataType, pagination.pageSize, searchText, fetchDataList]", {"range": "98", "text": "99"}, "Update the dependencies array to be: [checkRunningTask, pollingInterval]", {"range": "100", "text": "101"}, [3821, 3909], "[selectedAccount, selectedDataType, pagination.pageSize, searchText, fetchDataList]", [6044, 6046], "[checkRunningTask, pollingInterval]"]