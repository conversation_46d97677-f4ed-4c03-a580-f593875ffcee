{"ast": null, "code": "import axios from 'axios';\n// 根据环境变量确定API基础URL\nconst getBaseURL = () => {\n  // 在生产环境中使用环境变量或默认的生产API地址\n  if (process.env.NODE_ENV === 'production') {\n    return process.env.REACT_APP_API_URL || 'https://api.sm.shishu.me';\n  }\n  // 开发环境使用本地地址\n  return 'http://localhost:8000/api';\n};\nconst api = axios.create({\n  baseURL: getBaseURL(),\n  timeout: 300000 // 5分钟超时，适合长时间操作\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "getBaseURL", "process", "env", "NODE_ENV", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { message } from 'antd';\n\n// 根据环境变量确定API基础URL\nconst getBaseURL = () => {\n  // 在生产环境中使用环境变量或默认的生产API地址\n  if (process.env.NODE_ENV === 'production') {\n    return process.env.REACT_APP_API_URL || 'https://api.sm.shishu.me';\n  }\n  // 开发环境使用本地地址\n  return 'http://localhost:8000/api';\n};\n\nconst api = axios.create({\n  baseURL: getBaseURL(),\n  timeout: 300000, // 5分钟超时，适合长时间操作\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOF,OAAO,CAACC,GAAG,CAACE,iBAAiB,IAAI,0BAA0B;EACpE;EACA;EACA,OAAO,2BAA2B;AACpC,CAAC;AAED,MAAMC,GAAG,GAAGN,KAAK,CAACO,MAAM,CAAC;EACvBC,OAAO,EAAEP,UAAU,CAAC,CAAC;EACrBQ,OAAO,EAAE,MAAM,CAAE;AACnB,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}