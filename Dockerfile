FROM python:3.11-slim

# 配置阿里云镜像源（加速国内访问）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/* 2>/dev/null || true && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/* 2>/dev/null || true

# 安装基础系统依赖（Playwright 需要）
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 配置pip使用阿里云镜像
RUN pip config set global.index-url http://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装Playwright浏览器（使用国内镜像加速）
ENV PLAYWRIGHT_DOWNLOAD_HOST=https://npmmirror.com/mirrors/playwright/
RUN playwright install chromium
RUN playwright install-deps chromium

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app
USER appuser

# 启动命令（生产环境，移除--reload）
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]