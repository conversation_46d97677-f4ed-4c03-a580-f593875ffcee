# Nginx配置文件
# 将此配置添加到服务器的Nginx配置中

# 前端配置 - sm.shishu.me
# server {
#     listen 80;
#     server_name sm.shishu.me;
    
#     # 重定向到HTTPS
#     return 301 https://$server_name$request_uri;
# }

# API配置 - api.sm.shishu.me
# server {
#     listen 80;
#     server_name api.sm.shishu.me;
    
#     # 重定向到HTTPS
#     return 301 https://$server_name$request_uri;
# }

server {
    # listen 443 ssl http2;
    listen 80;
    server_name sm.shishu.me;
    
    # SSL证书配置（请替换为实际证书路径）
    # ssl_certificate /path/to/your/ssl/certificate.crt;
    # ssl_certificate_key /path/to/your/ssl/private.key;
    
    # SSL配置
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    # ssl_prefer_server_ciphers off;
    
    # 代理到前端容器
    location / {
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
        add_header Referrer-Policy "no-referrer-when-downgrade";
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'";
        
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

server {
    # listen 443 ssl http2;
    listen 80;
    server_name api.sm.shishu.me;
    
    # SSL证书配置（请替换为实际证书路径）
    # ssl_certificate /path/to/your/ssl/certificate.crt;
    # ssl_certificate_key /path/to/your/ssl/private.key;
    
    # SSL配置
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    # ssl_prefer_server_ciphers off;
    
    # 处理OPTIONS请求
    location / {
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
        add_header Referrer-Policy "no-referrer-when-downgrade";
        
        # CORS配置
        add_header Access-Control-Allow-Origin "http://sm.shishu.me";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range";
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "http://sm.shishu.me";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        access_log off;
    }
}
