#!/bin/bash

# 快速部署脚本
# 一键完成本地准备和服务器部署

set -e

echo "🚀 社交媒体管理系统 - 快速部署脚本"
echo "=================================="

# 检查必要的工具
check_tools() {
    echo "🔍 检查必要工具..."
    
    if ! command -v rsync &> /dev/null; then
        echo "❌ rsync未安装，请先安装rsync"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        echo "❌ ssh未安装，请先安装ssh"
        exit 1
    fi
    
    echo "✅ 工具检查完成"
}

# 收集部署信息
collect_info() {
    echo ""
    echo "📋 请输入部署信息："
    
    read -p "服务器IP地址: " SERVER_IP
    read -p "服务器用户名 (默认: root): " SERVER_USER
    SERVER_USER=${SERVER_USER:-root}
    
    read -p "服务器部署路径 (默认: /opt/social-media-manager): " SERVER_PATH
    SERVER_PATH=${SERVER_PATH:-/opt/social-media-manager}
    
    read -p "JWT密钥 (留空将生成随机密钥): " SECRET_KEY
    if [ -z "$SECRET_KEY" ]; then
        SECRET_KEY=$(openssl rand -base64 32)
        echo "生成的JWT密钥: $SECRET_KEY"
    fi
        
    echo ""
    echo "📝 部署信息确认："
    echo "服务器: $SERVER_USER@$SERVER_IP"
    echo "路径: $SERVER_PATH"
    echo ""
    
    read -p "确认信息正确？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 部署取消"
        exit 1
    fi
}

# 生成配置文件
generate_config() {
    echo "📝 生成配置文件..."
    
    # 生成 .env.production
    cat > .env.production << EOF
# 生产环境配置文件
# 数据库配置
DATABASE_URL=sqlite:///./data/social_media.db

# JWT配置
SECRET_KEY=$SECRET_KEY
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 环境标识
ENVIRONMENT=production
EOF

    # 更新上传脚本配置
    sed -i.bak "s/SERVER_USER=\"root\"/SERVER_USER=\"$SERVER_USER\"/" upload-to-server.sh
    sed -i.bak "s/SERVER_HOST=\"your-server-ip\"/SERVER_HOST=\"$SERVER_IP\"/" upload-to-server.sh
    sed -i.bak "s|SERVER_PATH=\"/opt/social-media-manager\"|SERVER_PATH=\"$SERVER_PATH\"|" upload-to-server.sh
    
    echo "✅ 配置文件生成完成"
}

# 测试服务器连接
test_connection() {
    echo "🔗 测试服务器连接..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $SERVER_USER@$SERVER_IP exit 2>/dev/null; then
        echo "✅ 服务器连接正常"
    else
        echo "❌ 无法连接到服务器，请检查："
        echo "   1. 服务器IP地址是否正确"
        echo "   2. SSH密钥是否配置正确"
        echo "   3. 服务器是否允许SSH连接"
        exit 1
    fi
}

# 上传文件
upload_files() {
    echo "📤 上传项目文件到服务器..."
    
    chmod +x upload-to-server.sh
    ./upload-to-server.sh
    
    echo "✅ 文件上传完成"
}

# 服务器部署
deploy_on_server() {
    echo "🚀 在服务器上执行部署..."
    
    ssh $SERVER_USER@$SERVER_IP << EOF
        cd $SERVER_PATH
        
        echo "🔧 设置执行权限..."
        chmod +x server-deploy.sh
        
        echo "🚀 开始部署..."
        ./server-deploy.sh
        
        echo "📊 检查部署状态..."
        docker-compose ps
EOF
    
    echo "✅ 服务器部署完成"
}

# 显示部署结果
show_result() {
    echo ""
    echo "🎉 部署完成！"
    echo "=================================="
    echo "📱 前端访问地址: https://sm.shishu.me"
    echo "🔧 API访问地址: https://api.sm.shishu.me"
    echo ""
    echo "⚠️  还需要完成以下配置："
    echo "1. 配置域名解析："
    echo "   sm.shishu.me     A    $SERVER_IP"
    echo "   api.sm.shishu.me A    $SERVER_IP"
    echo ""
    echo "2. 配置Nginx反向代理（参考 nginx-config.conf）"
    echo "3. 配置SSL证书"
    echo ""
    echo "📊 查看服务状态: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && docker-compose ps'"
    echo "📋 查看日志: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && docker-compose logs -f'"
}

# 主流程
main() {
    check_tools
    collect_info
    generate_config
    test_connection
    upload_files
    deploy_on_server
    show_result
}

# 执行主流程
main
