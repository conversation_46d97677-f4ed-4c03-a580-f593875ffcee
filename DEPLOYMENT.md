# 社交媒体管理系统 Docker 部署文档

## 📋 系统要求

### 服务器配置
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 20GB 可用空间
- **CPU**: 2核心以上

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- curl (用于健康检查)

## 🚀 快速部署

### 1. 安装 Docker 和 Docker Compose

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

# 退出重登录，使修改生效。

#### CentOS/RHEL
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 克隆项目代码
```bash
git clone https://github.com/leaf3000/social-media-manager.git
cd social-media-manager
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

**重要配置项说明**:
- `DB_PASSWORD`: 数据库密码（必须修改）
- `SECRET_KEY`: JWT密钥（必须生成新的）
- `FEISHU_APP_ID`: 飞书应用ID
- `FEISHU_APP_SECRET`: 飞书应用密钥

#### 生成安全的SECRET_KEY
```bash
python3 -c "import secrets; print(secrets.token_urlsafe(64))"
```

### 4. 执行部署
```bash
# 使用部署脚本（推荐）
./deploy.sh

# 或手动部署
docker-compose up --build -d
```

## 📁 项目结构

```
social-media-manager/
├── Dockerfile              # 后端Docker配置
├── docker-compose.yml      # 服务编排配置
├── .dockerignore           # Docker忽略文件
├── deploy.sh               # 部署脚本
├── frontend/
│   ├── Dockerfile          # 前端Docker配置
│   └── nginx.conf          # Nginx配置
├── mysql/
│   └── init/
│       └── 01-init.sql     # 数据库初始化脚本
└── .env                    # 环境变量配置
```

## 🔧 服务配置

### 服务端口
- **前端**: 80 (HTTP)
- **后端API**: 内部8000端口，通过nginx代理
- **MySQL**: 3306

### 数据持久化
- MySQL数据: `mysql_data` volume
- 用户数据: `./user_data` 目录挂载
- 应用数据: `./data` 目录挂载

## 🛠️ 常用操作

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mysql
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 停止服务
```bash
docker-compose down
```

### 更新代码
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up --build -d
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 2. SSL证书配置（生产环境）
建议使用Let's Encrypt免费SSL证书：

```bash
# 安装certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 修改nginx配置支持HTTPS
```

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :3306

# 停止占用端口的服务
sudo systemctl stop apache2  # 如果安装了Apache
sudo systemctl stop nginx    # 如果安装了Nginx
```

#### 2. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 重置数据库
docker-compose down
docker volume rm social-media-manager_mysql_data
docker-compose up -d
```

#### 3. 前端无法访问后端API
- 检查nginx配置是否正确
- 确认后端服务是否正常启动
- 检查网络连接

#### 4. 内存不足
```bash
# 检查系统资源
free -h
df -h

# 清理Docker资源（谨慎使用）
# 只清理悬空镜像和停止的容器
docker system prune -f
# 如果需要清理所有未使用的镜像，请谨慎使用：
# docker system prune -a -f
```

### 日志分析
```bash
# 查看系统日志
sudo journalctl -u docker

# 查看容器资源使用
docker stats
```

## 📊 监控和维护

### 健康检查
系统提供了自动健康检查，可以通过以下方式查看：

```bash
# 检查服务健康状态
curl http://localhost/
curl http://localhost/api/

# 查看容器健康状态
docker-compose ps
```

### 备份数据
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p social_media_manager > backup.sql

# 备份用户数据
tar -czf user_data_backup.tar.gz user_data/
```

### 恢复数据
```bash
# 恢复数据库
docker-compose exec -T mysql mysql -u root -p social_media_manager < backup.sql

# 恢复用户数据
tar -xzf user_data_backup.tar.gz
```

## 🔄 更新升级

### 应用更新
```bash
# 1. 备份数据
./backup.sh

# 2. 拉取最新代码
git pull

# 3. 重新部署
./deploy.sh
```

### Docker镜像更新
```bash
# 更新基础镜像
docker-compose pull
docker-compose up --build -d
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件
2. 检查系统资源
3. 参考故障排除章节
4. 提交Issue到GitHub仓库

---

**注意**: 生产环境部署前请务必：
- 修改默认密码
- 配置SSL证书
- 设置防火墙规则
- 定期备份数据
