#!/bin/bash

# 安全部署脚本 - 不会删除服务器上的其他文件

set -e

# 配置服务器信息
SERVER_USER="ops"
SERVER_HOST="*************"
SERVER_PATH="/home/<USER>/social-media-manager"

echo "🚀 开始安全部署到服务器..."

# 检查必要工具
if ! command -v rsync &> /dev/null; then
    echo "❌ rsync未安装，请先安装rsync"
    exit 1
fi

# 创建排除文件列表
cat > .rsync-exclude << EOF
.git/
.gitignore
node_modules/
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.DS_Store
.vscode/
.idea/
*.swp
*.swo
*~
.env
.env.local
.env.development
.env.test
social_media.db
social_media_manager.db
user_data/
data/
logs/
frontend/build/
frontend/node_modules/
test/
EOF

echo "📦 准备上传文件..."

# 在服务器上创建专用目录
echo "📁 创建部署目录..."
ssh ${SERVER_USER}@${SERVER_HOST} "mkdir -p ${SERVER_PATH}"

# 安全上传文件（不使用--delete）
echo "📤 上传项目文件..."
rsync -avz --progress \
    --exclude-from=.rsync-exclude \
    ./ ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/

# 上传环境配置文件
echo "📤 上传环境配置文件..."
scp .env.production ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/

# 设置执行权限
echo "🔧 设置执行权限..."
ssh ${SERVER_USER}@${SERVER_HOST} "chmod +x ${SERVER_PATH}/server-deploy.sh"

# 在服务器上执行部署
echo "🚀 在服务器上执行部署..."
ssh ${SERVER_USER}@${SERVER_HOST} << EOF
    cd ${SERVER_PATH}
    
    echo "📊 当前目录内容："
    ls -la
    
    echo "🔧 设置目录权限..."
    mkdir -p data user_data logs
    chmod 755 data user_data logs
    
    echo "🚀 开始Docker部署..."
    ./server-deploy.sh
EOF

# 清理临时文件
rm -f .rsync-exclude

echo "✅ 安全部署完成！"
echo ""
echo "📱 前端访问地址: https://sm.shishu.me"
echo "🔧 API访问地址: https://api.sm.shishu.me"
echo ""
echo "📊 查看服务状态:"
echo "ssh ${SERVER_USER}@${SERVER_HOST} 'cd ${SERVER_PATH} && docker-compose ps'"
echo ""
echo "📋 查看日志:"
echo "ssh ${SERVER_USER}@${SERVER_HOST} 'cd ${SERVER_PATH} && docker-compose logs -f'"
